<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sepet Güncelleme Testi</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .code-block {
            background: #333;
            color: #fff;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>WooCommerce Sepet AJAX Güncelleme Testi</h1>
    
    <div class="test-section">
        <h2>Ya<PERSON><PERSON><PERSON>iklik<PERSON></h2>
        <ul>
            <li><strong>functions.php:</strong> Fragment fonksiyonuna sepet tutarı güncelleme eklendi</li>
            <li><strong>product.js:</strong> AJAX başarılı olduğunda header sepet ikonunu güncelleme eklendi</li>
            <li><strong>cart-sidebar.js:</strong> Sepet sayısı ve tutarı güncelleme fonksiyonu iyileştirildi</li>
            <li><strong>CSS Seçici Düzeltmesi:</strong> Ürün fiyatı ile sepet tutarı çakışması çözüldü</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🔧 Çözülen Sorun</h2>
        <p class="error"><strong>Problem:</strong> Sepetten ürün çıkarıldığında ürün sayfasındaki fiyat 0 TL oluyordu.</p>
        <p class="success"><strong>Çözüm:</strong> CSS seçici çakışması düzeltildi. Artık sadece header sepet tutarı güncelleniyor.</p>
    </div>

    <div class="test-section">
        <h2>Test Adımları</h2>
        <ol>
            <li>Bir ürün sayfasına gidin</li>
            <li>Sepete ekle butonuna tıklayın</li>
            <li>Header'daki sepet ikonundaki sayı ve tutarın güncellendiğini kontrol edin</li>
            <li>Sepet sidebar'ını açın ve içeriğin doğru olduğunu kontrol edin</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>Eklenen Kod Parçaları</h2>
        
        <h3>functions.php - Fragment Güncelleme (Düzeltildi)</h3>
        <div class="code-block">
// Sepet tutarini guncelle - sadece header sepet ikonu icin
$cart_total = WC()->cart->get_cart_subtotal();
$fragments['.demir-cart .amount'] = '&lt;span class="amount"&gt;' . $cart_total . '&lt;/span&gt;';
        </div>

        <h3>product.js - AJAX Başarı Callback'i</h3>
        <div class="code-block">
// Header sepet ikonunu manuel olarak guncelle (ek guvenlik)
if (typeof dmrthema_ajax !== 'undefined') {
    $.ajax({
        url: dmrthema_ajax.ajax_url,
        type: 'POST',
        data: {
            action: 'get_cart_count',
            nonce: dmrthema_ajax.nonce
        },
        success: function(cartResponse) {
            if (cartResponse.success) {
                $('.cart-item-count').text(cartResponse.data.count);
                $('.demir-cart .amount').html(cartResponse.data.total);
            }
        }
    });
}
        </div>

        <h3>cart-sidebar.js - Güncellenmiş updateCartCount (Düzeltildi)</h3>
        <div class="code-block">
// Sepet tutarini guncelle - sadece header sepet ikonu icin
const cartAmount = document.querySelector('.demir-cart .amount');
if (cartAmount) {
    cartAmount.innerHTML = response.data.total;
}
        </div>
    </div>

    <div class="test-section">
        <h2>Beklenen Sonuç</h2>
        <p class="success">✓ Sepete ürün eklendikten sonra header'daki sepet ikonu hem sayı hem de tutar olarak AJAX ile güncellenmelidir.</p>
        <p class="success">✓ Sayfa yenilenmeden sepet bilgileri doğru şekilde görünmelidir.</p>
        <p class="success">✓ Sepet sidebar'ı açıldığında güncel içerik görünmelidir.</p>
    </div>

    <div class="test-section">
        <h2>Sorun Giderme</h2>
        <p>Eğer güncelleme çalışmıyorsa:</p>
        <ul>
            <li>Tarayıcı konsolunu kontrol edin (F12)</li>
            <li>JavaScript hatalarını kontrol edin</li>
            <li>AJAX isteklerinin başarılı olduğunu kontrol edin</li>
            <li>CSS seçicilerinin doğru olduğunu kontrol edin</li>
        </ul>
    </div>
</body>
</html>
