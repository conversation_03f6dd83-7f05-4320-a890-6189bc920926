<?php

// Walker Sinifi Mega Menu icin
class Dmr_Walker_Nav_Menu extends Walker_Nav_Menu {

    // Alt menu seviyesi baslatma
    function start_lvl( &$output, $depth = 0, $args = array() ) {
        // has-mega-menu veya trend-mega-menu sinifina sahip menuler icin ozel container
        if ( isset($args->walker->parent_item) &&
             (in_array( 'has-mega-menu', $args->walker->parent_item->classes ) ||
              in_array( 'trend-mega-menu', $args->walker->parent_item->classes )) ) {
            $parent_item = $args->walker->parent_item;
            $selected_page_id = get_post_meta( $parent_item->ID, '_menu_item_selected_page', true );

            // Alt menuleri kontrol et
            $has_children = $this->has_children_menu_items( $parent_item->ID );

            $output .= '<ul class="sub-menu"><div class="sub-menu-container">';

            // Kural 1: Bloklu + has-mega-menu + alt menuleri olan mega menuler
            // SADECE sayfa icerigini goster, alt menuleri gosterme
            if ( $selected_page_id && $selected_page_id != '0' && $has_children ) {
                $page_content = get_post_field( 'post_content', $selected_page_id );
                if ( $page_content ) {
                    $content = apply_filters( 'the_content', $page_content );
                    $output .= '<div class="mega-menu-page-content">';
                    $output .= $content;
                    $output .= '</div>';
                }
                // Alt menuleri gosterme - sadece sayfa icerigi
                return;
            }

            // Kural 2: Bloklu + has-mega-menu + alt menusu olmayan mega menuler
            // Sayfa icerigini goster
            if ( $selected_page_id && $selected_page_id != '0' && !$has_children ) {
                $page_content = get_post_field( 'post_content', $selected_page_id );
                if ( $page_content ) {
                    $content = apply_filters( 'the_content', $page_content );
                    $output .= '<div class="mega-menu-page-content">';
                    $output .= $content;
                    $output .= '</div>';
                }
            }

            // Kural 3: Bloksuz + has-mega-menu + alt menuleri olan mega menuler
            // Sayfa icerigini gosterme, SADECE alt menulerden olussun
            // Bu durumda sayfa icerigi eklenmez, sadece alt menuler gosterilir

        } else {
            // Normal menu
            $output .= '<ul class="sub-menu">';
        }
    }

    function end_lvl( &$output, $depth = 0, $args = array() ) {
        if ( isset($args->walker->parent_item) &&
             (in_array( 'has-mega-menu', $args->walker->parent_item->classes ) ||
              in_array( 'trend-mega-menu', $args->walker->parent_item->classes )) ) {
            $output .= '</div></ul>';
        } else {
            $output .= '</ul>';
        }
    }

    // start_el fonksiyonu, parent_item'i bir sonraki seviyeye aktarmak icin kullanilir
    function start_el( &$output, $item, $depth = 0, $args = array(), $id = 0 ) {
        $args->walker->parent_item = $item;
        parent::start_el( $output, $item, $depth, $args, $id );
    }

    // end_el fonksiyonu - mega menu icin ozel islem
    function end_el( &$output, $item, $depth = 0, $args = array() ) {
        // Sadece depth 0 (ana menu) seviyesinde mega menu kontrolu yap
        if ( $depth == 0 && (in_array( 'has-mega-menu', $item->classes ) ||
                             in_array( 'trend-mega-menu', $item->classes )) ) {
            $selected_page_id = get_post_meta( $item->ID, '_menu_item_selected_page', true );
            $has_children = $this->has_children_menu_items( $item->ID );

            // Eger alt menu yoksa ve sayfa icerigi de yoksa mega menu olusturma
            if ( !$has_children && ( !$selected_page_id || $selected_page_id == '0' ) ) {
                // Bos mega menu olusturma - normal menu gibi davran
                $output .= "</li>\n";
                return;
            }

            // Eger start_lvl cagrilmadiysa (alt menu yok) mega menu container olustur
            if ( !$has_children ) {
                $output .= '<ul class="sub-menu"><div class="sub-menu-container">';

                // Bloklu mega menu - sayfa icerigini goster
                if ( $selected_page_id && $selected_page_id != '0' ) {
                    $page_content = get_post_field( 'post_content', $selected_page_id );
                    if ( $page_content ) {
                        $content = apply_filters( 'the_content', $page_content );
                        $output .= '<div class="mega-menu-page-content">';
                        $output .= $content;
                        $output .= '</div>';
                    }
                }

                $output .= '</div></ul>';
            }
        }

        $output .= "</li>\n";
    }

    // Alt menu ogelerinin olup olmadigini kontrol eden yardimci fonksiyon
    private function has_children_menu_items( $menu_item_id ) {
        $menu_items = wp_get_nav_menu_items( get_nav_menu_locations()['primary'] );
        if ( !$menu_items ) return false;

        foreach ( $menu_items as $menu_item ) {
            if ( $menu_item->menu_item_parent == $menu_item_id ) {
                return true;
            }
        }
        return false;
    }
}


function dmrthema_setup() {
    // Otomatik feed linkleri ekler
    add_theme_support( 'automatic-feed-links' );

    // Başlık etiketini WordPress'in yönetmesine izin verir
    add_theme_support( 'title-tag' );

    // Öne çıkan görselleri etkinleştirir
    add_theme_support( 'post-thumbnails' );

    // Menüleri kaydeder
    register_nav_menus( array(
        'primary' => esc_html__( 'Primary Menu', 'dmrthema' ),
    ) );

    // HTML5 desteği
    add_theme_support( 'html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ) );

    // WooCommerce desteği
    add_theme_support( 'woocommerce' );

    // WooCommerce product gallery desteği
    add_theme_support( 'wc-product-gallery-zoom' );
    add_theme_support( 'wc-product-gallery-lightbox' );
    add_theme_support( 'wc-product-gallery-slider' );
}
add_action( 'after_setup_theme', 'dmrthema_setup' );

// Stil dosyalarını ve scriptleri ekler
function dmrthema_scripts() {
    // Ana stil dosyası
    wp_enqueue_style( 'dmrthema-style', get_stylesheet_uri(), array(), '1.0.2' );

    // Sadece ana sayfada slider scriptlerini yükle
    // Sepet sidebar scripti - tum sayfalarda yukle
    wp_enqueue_script( 'cart-sidebar', get_template_directory_uri() . '/assets/js/cart-sidebar.js', array(), '1.0.0', true );

    // User avatar dropdown scripti - tum sayfalarda yukle
    wp_enqueue_script( 'user-avatar', get_template_directory_uri() . '/assets/js/user-avatar.js', array(), '1.0.0', true );

    // Education panel scripti - tum sayfalarda yukle
    wp_enqueue_script( 'education-panel', get_template_directory_uri() . '/assets/js/education-panel.js', array('jquery'), '1.0.0', true );

    // Sepet sidebar fix CSS - en yuksek oncelik
    wp_enqueue_style( 'dmrthema-cart-sidebar-fix', get_template_directory_uri() . '/assets/css/cart-sidebar-fix.css', array('dmrthema-style'), '1.0.0' );

    // AJAX Search CSS ve JS - tum sayfalarda yukle
    wp_enqueue_style( 'dmrthema-ajax-search', get_template_directory_uri() . '/assets/css/ajax-search.css', array('dmrthema-style'), '1.0.0' );
    wp_enqueue_script( 'dmrthema-ajax-search', get_template_directory_uri() . '/assets/js/ajax-search.js', array('jquery'), '1.0.0', true );

    // AJAX Search parametrelerini JavaScript'e gonder - her zaman yukle
    wp_localize_script( 'dmrthema-ajax-search', 'dmrthema_ajax_search', array(
        'ajax_url' => admin_url( 'admin-ajax.php' ),
        'nonce' => wp_create_nonce( 'dmrthema_ajax_search_nonce' )
    ) );

    // Mega menu JavaScript
    wp_enqueue_script( 'mega-menu', get_template_directory_uri() . '/assets/js/mega-menu.js', array(), '1.0.0', true );

    // Trend mega menu JavaScript
    wp_enqueue_script( 'trend-mega-menu', get_template_directory_uri() . '/assets/js/trend-mega-menu.js', array(), '1.0.0', true );

    // WooCommerce AJAX parametrelerini JavaScript'e gonder
    if ( class_exists( 'WooCommerce' ) ) {
        wp_localize_script( 'cart-sidebar', 'dmrthema_ajax', array(
            'ajax_url' => admin_url( 'admin-ajax.php' ),
            'nonce' => wp_create_nonce( 'dmrthema_nonce' )
        ) );
    }

    if ( is_front_page() ) {
        // Swiper CSS
        wp_enqueue_style( 'swiper-css', 'https://unpkg.com/swiper/swiper-bundle.min.css' );

        // Swiper JS
        wp_enqueue_script( 'swiper-js', 'https://unpkg.com/swiper/swiper-bundle.min.js', array(), null, true );

        // Slider başlatma scripti
        wp_enqueue_script( 'slider-init', get_template_directory_uri() . '/js/slider-init.js', array('swiper-js'), null, true );

        // Slider ayarlarını JavaScript'e aktar
        $slider_autoplay = get_option('dmrthema_slider_autoplay', '1');
        $slider_delay = get_option('dmrthema_slider_delay', '5000');

        wp_localize_script( 'slider-init', 'dmrthemaSliderSettings', array(
            'autoplay' => $slider_autoplay,
            'delay' => $slider_delay
        ));
    }

    // Font Awesome - tum sayfalarda yukle
    wp_enqueue_style( 'font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css', array(), '6.0.0' );

    // WooCommerce stilleri ve scriptleri - tum sayfalarda yukle
    if ( class_exists( 'WooCommerce' ) ) {
        // WooCommerce CSS'ini tum sayfalarda yukle (sepet sidebar icin)
        wp_enqueue_style( 'dmrthema-woocommerce', get_template_directory_uri() . '/assets/css/woocommerce.css', array(), '1.0.4' );

        if ( is_product() ) {
            wp_enqueue_style( 'dmrthema-product', get_template_directory_uri() . '/assets/css/product.css', array(), '1.0.0' );
            wp_enqueue_script( 'dmrthema-product-js', get_template_directory_uri() . '/assets/js/product.js', array('jquery'), '1.0.0', true );
            wp_enqueue_script( 'dmrthema-quantity', get_template_directory_uri() . '/assets/js/quantity.min.js', array('jquery'), '1.0.0', true );
        }

        // Shop sayfasinda filtreler JavaScript'ini yukle
        if ( is_shop() || is_product_category() || is_product_tag() ) {
            wp_enqueue_script( 'dmrthema-shop-filters', get_template_directory_uri() . '/assets/js/shop-filters.js', array('jquery'), '1.0.0', true );
        }
    }
}
add_action( 'wp_enqueue_scripts', 'dmrthema_scripts' );

// Slider Custom Post Type'ı kaydet
function dmrthema_register_slider_cpt() {
    $args = array(
        'public'       => true,
        'label'        => 'Slider',
        'labels'       => array(
            'name'          => 'Slider',
            'singular_name' => 'Slider Öğesi',
            'add_new_item'  => 'Yeni Slider Öğesi Ekle',
            'edit_item'     => 'Slider Öğesini Düzenle',
            'new_item'      => 'Yeni Slider Öğesi',
            'view_item'     => 'Slider Öğesini Görüntüle',
            'search_items'  => 'Slider Öğesi Ara',
            'not_found'     => 'Slider öğesi bulunamadı',
        ),
        'supports'     => array('title', 'thumbnail', 'editor'),
        'show_in_menu' => 'dmrthema_settings', // DmrThema menüsünün altına ekle
        'menu_icon'    => 'dashicons-images-alt2',
    );
    register_post_type('slider', $args);
}
add_action('init', 'dmrthema_register_slider_cpt');

// Tema Ayarları Menüsünü oluştur
function dmrthema_admin_menu() {
    add_menu_page(
        'DmrThema Ayarları',
        'DmrThema',
        'manage_options',
        'dmrthema_settings',
        'dmrthema_settings_page_html',
        'dashicons-admin-customizer',
        20
    );
}
add_action('admin_menu', 'dmrthema_admin_menu');

// Slider ayarlarını kaydet
function dmrthema_register_slider_settings() {
    register_setting('dmrthema_slider_settings', 'dmrthema_slider_autoplay');
    register_setting('dmrthema_slider_settings', 'dmrthema_slider_delay');
}
add_action('admin_init', 'dmrthema_register_slider_settings');

// Ana ayar sayfası
function dmrthema_settings_page_html() {
    // Ayarlar kaydedildi mesajı
    if (isset($_GET['settings-updated'])) {
        add_settings_error('dmrthema_slider_messages', 'dmrthema_slider_message', 'Ayarlar kaydedildi!', 'updated');
    }

    // Hata mesajlarını göster
    settings_errors('dmrthema_slider_messages');
    ?>
    <div class="wrap">
        <h1>DmrThema Ayarları</h1>

        <h2 class="nav-tab-wrapper">
            <a href="#slider-settings" class="nav-tab nav-tab-active">Slider Ayarları</a>
        </h2>

        <div id="slider-settings" class="tab-content">
            <form method="post" action="options.php">
                <?php
                settings_fields('dmrthema_slider_settings');
                do_settings_sections('dmrthema_slider_settings');

                $autoplay = get_option('dmrthema_slider_autoplay', '1');
                $delay = get_option('dmrthema_slider_delay', '5000');
                ?>

                <table class="form-table">
                    <tr>
                        <th scope="row">Otomatik Geçiş</th>
                        <td>
                            <label>
                                <input type="checkbox" name="dmrthema_slider_autoplay" value="1" <?php checked($autoplay, '1'); ?> />
                                Slider öğeleri arasında otomatik geçiş yapsın
                            </label>
                            <p class="description">Bu seçenek etkinleştirildiğinde slider otomatik olarak bir sonraki resme geçer.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Geçiş Süresi</th>
                        <td>
                            <input type="number" name="dmrthema_slider_delay" value="<?php echo esc_attr($delay); ?>" min="1000" max="10000" step="500" />
                            <span>milisaniye</span>
                            <p class="description">Slider'ın bir sonraki resme geçmesi için bekleyeceği süre (1000 = 1 saniye). Minimum 1000, maksimum 10000 milisaniye.</p>
                        </td>
                    </tr>
                </table>

                <?php submit_button('Ayarları Kaydet'); ?>
            </form>
        </div>
    </div>

    <style>
        .nav-tab-wrapper {
            margin-bottom: 20px;
        }
        .tab-content {
            background: #fff;
            padding: 20px;
            border: 1px solid #ccd0d4;
            border-top: none;
        }
        .form-table th {
            width: 200px;
        }
    </style>
    <?php
}

/**
 * WooCommerce Entegrasyonu
 */
if ( class_exists( 'WooCommerce' ) ) {
    // WooCommerce template functions dosyasını yükle
    require_once get_template_directory() . '/inc/woocommerce/dmrthema-woocommerce-template-functions.php';

    // WooCommerce template hooks dosyasını yükle
    require_once get_template_directory() . '/inc/woocommerce/dmrthema-woocommerce-template-hooks.php';
}

/**
 * WooCommerce için özel fonksiyonlar
 */

// Slider sayfasına ayarlar bölümü ekle
function dmrthema_slider_settings_page() {
    global $pagenow, $typenow;

    // Sadece slider listesi sayfasında göster
    if ( $pagenow == 'edit.php' && $typenow == 'slider' ) {


        // Ayar butonu ekle (sayfa başlığının yanına)
        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Biraz bekle ki sayfa tam yüklensin
            setTimeout(function() {
                // "Yeni Slider Öğesi Ekle" butonunu bul
                var addNewButton = document.querySelector('a.page-title-action');
                if (addNewButton) {
                    // Ayar butonunu oluştur
                    var settingsButton = document.createElement('a');
                    settingsButton.href = '#';
                    settingsButton.className = 'page-title-action';
                    settingsButton.innerHTML = 'Slider Ayarları';
                    settingsButton.style.marginLeft = '5px';
                    settingsButton.onclick = function(e) {
                        e.preventDefault();
                        document.getElementById('slider-settings-modal').style.display = 'block';
                        return false;
                    };

                    // Butonu ekle
                    addNewButton.parentNode.insertBefore(settingsButton, addNewButton.nextSibling);
                }
            }, 100);
        });
        </script>

        <?php
        $autoplay = get_option('dmrthema_slider_autoplay', '1');
        $delay = get_option('dmrthema_slider_delay', '5000');
        ?>

        <!-- Modal -->
        <div id="slider-settings-modal" style="display: none; position: fixed; z-index: 100000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
            <div style="background-color: white; margin: 5% auto; padding: 0; width: 600px; max-width: 90%; border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
                <!-- Modal Header -->
                <div style="background: #0073aa; color: white; padding: 20px; border-radius: 8px 8px 0 0; position: relative;">
                    <h2 style="margin: 0; color: white;">
                        <span class="dashicons dashicons-admin-settings" style="margin-right: 8px;"></span>
                        Slider Ayarları
                    </h2>
                    <button onclick="document.getElementById('slider-settings-modal').style.display='none'"
                            style="position: absolute; right: 15px; top: 15px; background: none; border: none; color: white; font-size: 24px; cursor: pointer; padding: 5px;">
                        &times;
                    </button>
                </div>

                <!-- Modal Content -->
                <div style="padding: 20px;">
                    <form method="post" action="">
                        <?php wp_nonce_field( 'save_slider_settings', 'slider_settings_nonce' ); ?>

                        <table class="form-table" style="margin-top: 0;">
                            <tr>
                                <th scope="row" style="width: 200px;">
                                    <label for="dmrthema_slider_autoplay">Otomatik Geçiş</label>
                                </th>
                                <td>
                                    <label style="display: flex; align-items: center; gap: 8px;">
                                        <input type="checkbox" id="dmrthema_slider_autoplay" name="dmrthema_slider_autoplay" value="1" <?php checked($autoplay, '1'); ?> />
                                        <span>Slider öğeleri arasında otomatik geçiş yapsın</span>
                                    </label>
                                    <p class="description" style="margin-top: 8px; color: #666;">
                                        Bu seçenek etkinleştirildiğinde slider otomatik olarak bir sonraki resme geçer.
                                        <br><strong>Şu anki durum:</strong> <?php echo $autoplay == '1' ? '<span style="color: green;">Açık</span>' : '<span style="color: red;">Kapalı</span>'; ?>
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="dmrthema_slider_delay">Geçiş Süresi</label>
                                </th>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <input type="number" id="dmrthema_slider_delay" name="dmrthema_slider_delay"
                                               value="<?php echo esc_attr($delay); ?>" min="1000" max="10000" step="500"
                                               style="width: 100px;" />
                                        <span>milisaniye</span>
                                    </div>
                                    <p class="description" style="margin-top: 8px; color: #666;">
                                        Slider'ın bir sonraki resme geçmesi için bekleyeceği süre (1000 = 1 saniye).
                                        <br>Minimum 1000, maksimum 10000 milisaniye.
                                    </p>
                                </td>
                            </tr>
                        </table>

                        <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; text-align: right;">
                            <button type="button" onclick="document.getElementById('slider-settings-modal').style.display='none'"
                                    class="button" style="margin-right: 10px;">İptal</button>
                            <input type="submit" name="submit_slider_settings" id="submit_slider_settings"
                                   class="button button-primary" value="Ayarları Kaydet" />
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Modal dışına tıklandığında kapat -->
        <script>
        document.getElementById('slider-settings-modal').onclick = function(event) {
            if (event.target === this) {
                this.style.display = 'none';
            }
        };

        // Form gönderildikten sonra modalı kapat
        <?php if (isset($_POST['submit_slider_settings'])): ?>
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('slider-settings-modal').style.display = 'none';
        });
        <?php endif; ?>
        </script>
        <?php
    }
}
add_action( 'admin_notices', 'dmrthema_slider_settings_page' );
add_action( 'admin_init', 'dmrthema_handle_slider_settings' );

// Slider ayarlarını işle
function dmrthema_handle_slider_settings() {
    global $pagenow, $typenow;

    // Sadece slider sayfasında ve POST varsa çalış
    if ( $pagenow == 'edit.php' && $typenow == 'slider' && isset( $_POST['submit_slider_settings'] ) ) {

        // Nonce kontrolü
        if ( ! wp_verify_nonce( $_POST['slider_settings_nonce'], 'save_slider_settings' ) ) {
            wp_die( 'Güvenlik kontrolü başarısız!' );
        }

        // Checkbox değerini kontrol et
        if ( isset( $_POST['dmrthema_slider_autoplay'] ) && $_POST['dmrthema_slider_autoplay'] == '1' ) {
            $autoplay_value = '1';
        } else {
            $autoplay_value = '0';
        }

        $delay_value = sanitize_text_field( $_POST['dmrthema_slider_delay'] );

        // Değerleri kaydet
        update_option( 'dmrthema_slider_autoplay', $autoplay_value );
        update_option( 'dmrthema_slider_delay', $delay_value );

        // Başarı mesajı için session kullan
        add_action( 'admin_notices', function() use ( $autoplay_value, $delay_value ) {
            echo '<div class="notice notice-success is-dismissible"><p>Slider ayarları kaydedildi! (Otomatik geçiş: ' . ($autoplay_value == '1' ? 'Açık' : 'Kapalı') . ', Süre: ' . $delay_value . 'ms)</p></div>';
        });

        // Sayfayı yenile (redirect)
        wp_redirect( admin_url( 'edit.php?post_type=slider&settings_saved=1' ) );
        exit;
    }
}

// Slider düzenleme sayfasında öne çıkarılan görsel meta box'ını gizle
function dmrthema_slider_admin_styles() {
    global $post_type;
    if ( $post_type == 'slider' ) {
        ?>
        <style>
            /* Öne çıkarılan görsel meta box'ını gizle */
            #postimagediv {
                display: none !important;
            }
        </style>
        <?php
    }
}
add_action( 'admin_head', 'dmrthema_slider_admin_styles' );

// Slider düzenleme sayfasında önerilen resim boyutu meta box'ını ekle
function dmrthema_add_slider_image_size_meta_box() {
    add_meta_box(
        'slider_image_size_info',
        'Resim Boyutu Bilgisi',
        'dmrthema_slider_image_size_meta_box_callback',
        'slider',
        'side',
        'high'
    );
}
add_action( 'add_meta_boxes', 'dmrthema_add_slider_image_size_meta_box' );

// Meta box içeriği
function dmrthema_slider_image_size_meta_box_callback() {
    ?>
    <div style="text-align: center; padding: 10px;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 8px; font-size: 16px; font-weight: 600; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);">
            Önerilen resim boyutu: 1920x550
        </div>
        <p style="margin-top: 10px; color: #666; font-size: 13px;">
            En iyi görüntü kalitesi için bu boyutları kullanın.
        </p>
    </div>
    <?php
}

// WooCommerce ürün sayısını ayarla
function dmrthema_woocommerce_product_columns() {
    return 3; // 3 sütun
}
add_filter( 'loop_shop_columns', 'dmrthema_woocommerce_product_columns' );

// WooCommerce sayfa başına ürün sayısını ayarla
function dmrthema_woocommerce_products_per_page() {
    return 12; // Sayfa başına 12 ürün
}
add_filter( 'loop_shop_per_page', 'dmrthema_woocommerce_products_per_page', 20 );

// Ana sayfa için WooCommerce loop ayarları
function dmrthema_homepage_woocommerce_loop_columns() {
    if ( is_front_page() ) {
        return 4; // Ana sayfada 4 sütun
    }
    return 3; // Diğer sayfalarda 3 sütun
}
add_filter( 'loop_shop_columns', 'dmrthema_homepage_woocommerce_loop_columns', 25 );

/**
 * Checkout ve order received sayfasi icin ozel header
 */
function dmrthema_checkout_custom_header() {
    if ( ( is_checkout() && ! is_wc_endpoint_url() ) || ( is_checkout() && is_wc_endpoint_url('order-received') ) ) {
        ?>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                document.body.classList.add('checkout-page');
            });
        </script>
        <?php
    }
}
add_action( 'wp_head', 'dmrthema_checkout_custom_header' );

/**
 * Checkout ve order received sayfasinda header'i ozelestir
 */
function dmrthema_modify_checkout_header() {
    if ( ( is_checkout() && ! is_wc_endpoint_url() ) || ( is_checkout() && is_wc_endpoint_url('order-received') ) ) {
        // Header'daki gereksiz elementleri gizle
        add_filter( 'dmrthema_show_search_form', '__return_false' );
        add_filter( 'dmrthema_show_user_menu', '__return_false' );
        add_filter( 'dmrthema_show_cart_link', '__return_false' );
    }
}
add_action( 'template_redirect', 'dmrthema_modify_checkout_header' );



/**
 * Custom WooCommerce Query Filters
 * Ozel WooCommerce sorgu filtreleri
 */

// Fiyat filtresi
function dmrthema_filter_products_by_price( $q ) {
    if ( ! is_admin() && $q->is_main_query() ) {
        if ( is_shop() || is_product_category() || is_product_tag() ) {
            $meta_query = $q->get( 'meta_query' );
            if ( ! is_array( $meta_query ) ) {
                $meta_query = array();
            }

            // Min fiyat filtresi
            if ( ! empty( $_GET['min_price'] ) ) {
                $meta_query[] = array(
                    'key'     => '_price',
                    'value'   => floatval( $_GET['min_price'] ),
                    'compare' => '>=',
                    'type'    => 'NUMERIC'
                );
            }

            // Max fiyat filtresi
            if ( ! empty( $_GET['max_price'] ) ) {
                $meta_query[] = array(
                    'key'     => '_price',
                    'value'   => floatval( $_GET['max_price'] ),
                    'compare' => '<=',
                    'type'    => 'NUMERIC'
                );
            }

            // Stok durumu filtresi
            if ( ! empty( $_GET['stock_status'] ) ) {
                $stock_statuses = explode( ',', sanitize_text_field( $_GET['stock_status'] ) );
                if ( count( $stock_statuses ) == 1 ) {
                    $meta_query[] = array(
                        'key'     => '_stock_status',
                        'value'   => $stock_statuses[0],
                        'compare' => '='
                    );
                } else {
                    $meta_query[] = array(
                        'key'     => '_stock_status',
                        'value'   => $stock_statuses,
                        'compare' => 'IN'
                    );
                }
            }

            if ( ! empty( $meta_query ) ) {
                $q->set( 'meta_query', $meta_query );
            }

            // Kategori filtresi
            if ( ! empty( $_GET['product_cat'] ) ) {
                $categories = explode( ',', sanitize_text_field( $_GET['product_cat'] ) );
                $tax_query = $q->get( 'tax_query' );
                if ( ! is_array( $tax_query ) ) {
                    $tax_query = array();
                }

                $tax_query[] = array(
                    'taxonomy' => 'product_cat',
                    'field'    => 'slug',
                    'terms'    => $categories,
                    'operator' => 'IN'
                );

                $q->set( 'tax_query', $tax_query );
            }

            // Etiket filtresi
            if ( ! empty( $_GET['product_tag'] ) ) {
                $tags = explode( ',', sanitize_text_field( $_GET['product_tag'] ) );
                $tax_query = $q->get( 'tax_query' );
                if ( ! is_array( $tax_query ) ) {
                    $tax_query = array();
                }

                $tax_query[] = array(
                    'taxonomy' => 'product_tag',
                    'field'    => 'slug',
                    'terms'    => $tags,
                    'operator' => 'IN'
                );

                $q->set( 'tax_query', $tax_query );
            }
        }
    }
}
add_action( 'pre_get_posts', 'dmrthema_filter_products_by_price' );

// WooCommerce sidebar'ı kaldır
function dmrthema_remove_woocommerce_sidebar() {
    if ( is_shop() || is_product_category() || is_product_tag() || is_product() ) {
        remove_action( 'woocommerce_sidebar', 'woocommerce_get_sidebar', 10 );
    }
}
add_action( 'wp', 'dmrthema_remove_woocommerce_sidebar' );

// WooCommerce için widget alanları
function dmrthema_woocommerce_widgets_init() {
    register_sidebar( array(
        'name'          => esc_html__( 'Shop Sidebar', 'dmrthema' ),
        'id'            => 'shop-sidebar',
        'description'   => esc_html__( 'Add widgets here to appear in shop pages.', 'dmrthema' ),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ) );

    register_sidebar( array(
        'name'          => esc_html__( 'Single Product Field', 'dmrthema' ),
        'id'            => 'single-product-field',
        'description'   => esc_html__( 'Add widgets here to appear on single product pages.', 'dmrthema' ),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ) );
}
add_action( 'widgets_init', 'dmrthema_woocommerce_widgets_init' );

/**
 * AJAX fonksiyonlari - Sepet sidebar icin
 */

// Sepet sayisini getir
function dmrthema_get_cart_count() {
    if ( class_exists( 'WooCommerce' ) && WC()->cart ) {
        wp_send_json_success( array(
            'count' => WC()->cart->get_cart_contents_count(),
            'total' => WC()->cart->get_cart_subtotal()
        ) );
    } else {
        wp_send_json_error( 'WooCommerce not available' );
    }
}
add_action( 'wp_ajax_get_cart_count', 'dmrthema_get_cart_count' );
add_action( 'wp_ajax_nopriv_get_cart_count', 'dmrthema_get_cart_count' );

// Mini cart icerigini getir
function dmrthema_get_mini_cart() {
    if ( class_exists( 'WooCommerce' ) ) {
        ob_start();
        woocommerce_mini_cart();
        $mini_cart = ob_get_clean();

        wp_send_json_success( array(
            'mini_cart' => $mini_cart
        ) );
    } else {
        wp_send_json_error( 'WooCommerce not available' );
    }
}
add_action( 'wp_ajax_get_mini_cart', 'dmrthema_get_mini_cart' );
add_action( 'wp_ajax_nopriv_get_mini_cart', 'dmrthema_get_mini_cart' );

// Sepetten urun kaldir
function dmrthema_remove_cart_item() {
    // Nonce kontrolu
    if ( ! wp_verify_nonce( $_POST['nonce'], 'dmrthema_nonce' ) ) {
        wp_send_json_error( 'Invalid nonce' );
    }

    if ( class_exists( 'WooCommerce' ) && WC()->cart ) {
        $cart_item_key = sanitize_text_field( $_POST['cart_item_key'] );

        if ( $cart_item_key ) {
            WC()->cart->remove_cart_item( $cart_item_key );
            wp_send_json_success( array(
                'message' => 'Item removed from cart'
            ) );
        } else {
            wp_send_json_error( 'Invalid cart item key' );
        }
    } else {
        wp_send_json_error( 'WooCommerce not available' );
    }
}
add_action( 'wp_ajax_remove_cart_item', 'dmrthema_remove_cart_item' );
add_action( 'wp_ajax_nopriv_remove_cart_item', 'dmrthema_remove_cart_item' );

/**
 * Sepet guncellendikinde fragment'lari yenile
 */
function dmrthema_add_to_cart_fragment( $fragments ) {
    if ( class_exists( 'WooCommerce' ) && WC()->cart ) {
        // Mini cart icerigini de fragment olarak ekle
        ob_start();
        woocommerce_mini_cart();
        $mini_cart = ob_get_clean();
        $fragments['.widget_shopping_cart_content'] = '<div class="widget_shopping_cart_content">' . $mini_cart . '</div>';

        // Sepet ürün sayısını guncelle
        $cart_count = WC()->cart->get_cart_contents_count();
        $fragments['.cart-item-count'] = '<span class="cart-item-count">' . $cart_count . '</span>';

        // Sepet tutarini guncelle - sadece header sepet ikonu icin
        $cart_total = WC()->cart->get_cart_subtotal();
        $fragments['.demir-cart .amount'] = '<span class="amount">' . $cart_total . '</span>';
    }
    return $fragments;
}
add_filter( 'woocommerce_add_to_cart_fragments', 'dmrthema_add_to_cart_fragment' );

/**
 * AJAX Search Functionality
 * WooCommerce urunleri icin AJAX tabанli canli arama
 */

// AJAX arama handler fonksiyonu
function dmrthema_ajax_search() {
    // Nonce kontrolu
    if ( ! wp_verify_nonce( $_POST['nonce'], 'dmrthema_ajax_search_nonce' ) ) {
        wp_die( 'Guvenlik kontrolu basarisiz!' );
    }

    $query = sanitize_text_field( $_POST['query'] );

    if ( empty( $query ) || strlen( $query ) < 1 ) {
        wp_send_json_success( array(
            'products' => array(),
            'total' => 0,
            'search_url' => '',
            'query' => $query
        ) );
    }

    // WooCommerce urun arama - basladigini kontrol et
    $args = array(
        'post_type' => 'product',
        'post_status' => 'publish',
        'posts_per_page' => 8,
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key' => '_stock_status',
                'value' => 'instock',
                'compare' => '='
            )
        ),
        'tax_query' => array(
            array(
                'taxonomy' => 'product_visibility',
                'field'    => 'name',
                'terms'    => array('exclude-from-search', 'exclude-from-catalog'),
                'operator' => 'NOT IN',
            ),
        ),
    );

    // Ozel arama filtresi ekle - sadece basladigini ara
    add_filter( 'posts_where', 'dmrthema_search_by_title_starts_with', 10, 2 );
    add_filter( 'posts_search', '__return_empty_string' ); // Varsayilan arama devre disi

    $products = new WP_Query( $args );

    // Filtreleri kaldir
    remove_filter( 'posts_where', 'dmrthema_search_by_title_starts_with', 10 );
    remove_filter( 'posts_search', '__return_empty_string' );

    $results = array();



    if ( $products->have_posts() ) {
        while ( $products->have_posts() ) {
            $products->the_post();
            $product = wc_get_product( get_the_ID() );

            if ( $product && $product->is_visible() ) {
                // Urun resmi al
                $image_id = $product->get_image_id();
                $image_url = '';
                if ( $image_id ) {
                    $image_url = wp_get_attachment_image_url( $image_id, 'woocommerce_thumbnail' );
                } else {
                    $image_url = wc_placeholder_img_src( 'woocommerce_thumbnail' );
                }

                $results[] = array(
                    'id' => get_the_ID(),
                    'title' => get_the_title(),
                    'url' => get_permalink(),
                    'price' => $product->get_price_html(),
                    'image' => $image_url,
                    'stock_status' => $product->get_stock_status(),
                    'type' => $product->get_type()
                );
            }
        }
        wp_reset_postdata();
    }

    // Arama sonuclari sayfasinin URL'si
    $search_url = add_query_arg( 's', $query, wc_get_page_permalink( 'shop' ) );

    wp_send_json_success( array(
        'products' => $results,
        'total' => $products->found_posts,
        'search_url' => $search_url,
        'query' => $query
    ) );
}
add_action( 'wp_ajax_dmrthema_ajax_search', 'dmrthema_ajax_search' );
add_action( 'wp_ajax_nopriv_dmrthema_ajax_search', 'dmrthema_ajax_search' );

// Populer aramalar fonksiyonu
function dmrthema_get_popular_searches() {
    // Nonce kontrolu
    if ( ! wp_verify_nonce( $_POST['nonce'], 'dmrthema_ajax_search_nonce' ) ) {
        wp_die( 'Guvenlik kontrolu basarisiz!' );
    }

    // Populer urunleri al (en cok satan veya featured)
    $args = array(
        'post_type' => 'product',
        'post_status' => 'publish',
        'posts_per_page' => 6,
        'meta_query' => array(
            array(
                'key' => '_stock_status',
                'value' => 'instock',
                'compare' => '='
            )
        ),
        'orderby' => 'menu_order',
        'order' => 'ASC'
    );

    $popular_products = new WP_Query( $args );
    $results = array();

    if ( $popular_products->have_posts() ) {
        while ( $popular_products->have_posts() ) {
            $popular_products->the_post();
            $product = wc_get_product( get_the_ID() );

            if ( $product && $product->is_visible() ) {
                $results[] = array(
                    'id' => get_the_ID(),
                    'title' => get_the_title(),
                    'url' => get_permalink(),
                    'type' => 'popular'
                );
            }
        }
        wp_reset_postdata();
    }

    wp_send_json_success( array(
        'searches' => $results,
        'total' => count($results),
        'type' => 'popular'
    ) );
}
add_action( 'wp_ajax_dmrthema_get_popular_searches', 'dmrthema_get_popular_searches' );
add_action( 'wp_ajax_nopriv_dmrthema_get_popular_searches', 'dmrthema_get_popular_searches' );

// Ozel arama filtresi - sadece basladigini ara
function dmrthema_search_by_title_starts_with( $where, $wp_query ) {
    global $wpdb;

    if ( isset( $_POST['query'] ) && !empty( $_POST['query'] ) ) {
        $search_term = sanitize_text_field( $_POST['query'] );
        $search_term = $wpdb->esc_like( $search_term );

        // Sadece post_title'in basladigini ara
        $where .= " AND {$wpdb->posts}.post_title LIKE '{$search_term}%'";
    }

    return $where;
}

/**
 * Ana Sayfa Icin Populer Kurslar
 * Tutor LMS ile entegre en cok satan kurslar
 */
function dmrthema_get_popular_courses( $limit = 10 ) {
    // Tutor LMS aktif mi kontrol et
    if ( ! function_exists( 'tutor' ) ) {
        // Tutor LMS yoksa normal urunleri goster
        return dmrthema_get_featured_products( $limit );
    }

    // Once kurs urunlerini dene
    $args = array(
        'post_type' => 'product',
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key' => '_stock_status',
                'value' => 'instock',
                'compare' => '='
            ),
            array(
                'key' => '_tutor_course_product_id',
                'compare' => 'EXISTS'
            )
        ),
        'orderby' => array(
            'total_sales' => 'DESC',
            'menu_order' => 'ASC',
            'date' => 'DESC'
        )
    );

    $query = new WP_Query( $args );

    // Eger kurs urunu yoksa, ozellikli urunleri goster
    if ( ! $query->have_posts() ) {
        wp_reset_postdata();
        return dmrthema_get_featured_products( $limit );
    }

    return $query;
}

/**
 * Ozellikli Urunleri Al
 * Fallback olarak kullanilir
 */
function dmrthema_get_featured_products( $limit = 10 ) {
    $args = array(
        'post_type' => 'product',
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key' => '_stock_status',
                'value' => 'instock',
                'compare' => '='
            ),
            array(
                'key' => '_featured',
                'value' => 'yes',
                'compare' => '='
            )
        ),
        'orderby' => 'menu_order',
        'order' => 'ASC'
    );

    $query = new WP_Query( $args );

    // Eger ozellikli urun yoksa, en yeni urunleri goster
    if ( ! $query->have_posts() ) {
        wp_reset_postdata();
        $args = array(
            'post_type' => 'product',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'meta_query' => array(
                array(
                    'key' => '_stock_status',
                    'value' => 'instock',
                    'compare' => '='
                )
            ),
            'orderby' => 'date',
            'order' => 'DESC'
        );
        return new WP_Query( $args );
    }

    return $query;
}

/**
 * Ana Sayfa Icin En Yuksek Puanli Urunler
 * Yildiz puani en yuksek olan urunler
 */
function dmrthema_get_top_rated_products( $limit = 10 ) {
    $args = array(
        'post_type' => 'product',
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key' => '_stock_status',
                'value' => 'instock',
                'compare' => '='
            ),
            array(
                'key' => '_wc_average_rating',
                'value' => 0,
                'compare' => '>',
                'type' => 'DECIMAL'
            )
        ),
        'orderby' => 'meta_value_num',
        'meta_key' => '_wc_average_rating',
        'order' => 'DESC'
    );

    return new WP_Query( $args );
}

/**
 * Urun Kartini Render Et
 * WooCommerce magaza sayfasindaki gibi tam uyumlu urun karti
 */
function dmrthema_render_product_card( $product_id ) {
    global $woocommerce_loop;

    $product = wc_get_product( $product_id );

    if ( ! $product || ! $product->is_visible() ) {
        return;
    }

    // WooCommerce loop'u simule et
    $woocommerce_loop['name'] = 'homepage';

    // Post'u set et
    global $post;
    $post = get_post( $product_id );
    setup_postdata( $post );

    ?>
    <li <?php wc_product_class( '', $product ); ?>>
        <?php
        /**
         * Hook: woocommerce_before_shop_loop_item.
         */
        do_action( 'woocommerce_before_shop_loop_item' );

        /**
         * Hook: woocommerce_before_shop_loop_item_title.
         *
         * @hooked woocommerce_show_product_loop_sale_flash - 10
         * @hooked woocommerce_template_loop_product_thumbnail - 10
         */
        do_action( 'woocommerce_before_shop_loop_item_title' );

        /**
         * Hook: woocommerce_shop_loop_item_title.
         *
         * @hooked woocommerce_template_loop_product_title - 10
         */
        do_action( 'woocommerce_shop_loop_item_title' );

        /**
         * Hook: woocommerce_after_shop_loop_item_title.
         *
         * @hooked woocommerce_template_loop_rating - 5
         * @hooked woocommerce_template_loop_price - 10
         */
        do_action( 'woocommerce_after_shop_loop_item_title' );

        /**
         * Hook: woocommerce_after_shop_loop_item.
         *
         * @hooked woocommerce_template_loop_product_link_close - 5
         * @hooked woocommerce_template_loop_add_to_cart - 10
         */
        do_action( 'woocommerce_after_shop_loop_item' );
        ?>
    </li>
    <?php

    wp_reset_postdata();
}

/**
 * Menu Ogesine Ozel Alanlar Ekleme
 * Mega menu icin sayfa secimi
 */

// Menu ogesi ayarlar sayfasina "Blok Sec" alani ekle
function dmrthema_add_menu_item_custom_fields( $item_id, $item, $depth, $args ) {
    $selected_page = get_post_meta( $item_id, '_menu_item_selected_page', true );
    ?>
    <p class="field-selected-page description description-wide">
        <label for="edit-menu-item-selected-page-<?php echo $item_id; ?>">
            Blok Sec<br />
            <select id="edit-menu-item-selected-page-<?php echo $item_id; ?>" class="widefat code edit-menu-item-selected-page" name="menu-item-selected-page[<?php echo $item_id; ?>]">
                <option value="0"><?php _e('Sayfa Secin'); ?></option>
                <?php
                $pages = get_pages( array(
                    'sort_order' => 'ASC',
                    'sort_column' => 'post_title',
                    'post_status' => 'publish'
                ) );

                foreach ( $pages as $page ) {
                    $selected = selected( $selected_page, $page->ID, false );
                    echo '<option value="' . $page->ID . '" ' . $selected . '>' . esc_html( $page->post_title ) . '</option>';
                }
                ?>
            </select>
            <span class="description">Mega menu icinde gosterilecek sayfa icerigini secin.</span>
        </label>
    </p>
    <?php
}
add_action( 'wp_nav_menu_item_custom_fields', 'dmrthema_add_menu_item_custom_fields', 10, 4 );

// Menu ogesi kaydedildiginde ozel alanlari kaydet
function dmrthema_save_menu_item_custom_fields( $menu_id, $menu_item_db_id, $args ) {
    if ( isset( $_REQUEST['menu-item-selected-page'][$menu_item_db_id] ) ) {
        $selected_page_value = sanitize_text_field( $_REQUEST['menu-item-selected-page'][$menu_item_db_id] );
        update_post_meta( $menu_item_db_id, '_menu_item_selected_page', $selected_page_value );
    } else {
        delete_post_meta( $menu_item_db_id, '_menu_item_selected_page' );
    }
}
add_action( 'wp_update_nav_menu_item', 'dmrthema_save_menu_item_custom_fields', 10, 3 );

// Menu ogesi silindiginde meta verileri de sil
function dmrthema_delete_menu_item_custom_fields( $menu_item_id ) {
    delete_post_meta( $menu_item_id, '_menu_item_selected_page' );
}
add_action( 'delete_post', 'dmrthema_delete_menu_item_custom_fields' );

// Menu yonetimi sayfasinda ozel alan icin CSS ekle
function dmrthema_menu_admin_styles() {
    global $pagenow;

    if ( $pagenow == 'nav-menus.php' ) {
        ?>
        <style>
            .field-selected-page {
                margin-top: 15px !important;
                padding-top: 15px !important;
                border-top: 1px solid #e0e0e0 !important;
            }

            .field-selected-page label {
                font-weight: 600 !important;
                color: #333 !important;
            }

            .field-selected-page select {
                margin-top: 8px !important;
                margin-bottom: 5px !important;
            }

            .field-selected-page .description {
                font-style: italic !important;
                color: #666 !important;
                font-size: 12px !important;
            }
        </style>
        <?php
    }
}
add_action( 'admin_head', 'dmrthema_menu_admin_styles' );

// Debug: Menu HTML yapisini kontrol et
function dmrthema_debug_menu_html() {
    if ( current_user_can( 'manage_options' ) && isset( $_GET['debug_menu'] ) ) {
        echo '<div style="background: #f0f0f0; padding: 20px; margin: 20px; border: 1px solid #ccc;">';
        echo '<h3>Menu HTML Debug:</h3>';
        echo '<pre>';

        wp_nav_menu( array(
            'theme_location' => 'primary',
            'menu_id'        => 'debug-menu',
            'walker'         => new Dmr_Walker_Nav_Menu(),
            'echo'           => true
        ) );

        echo '</pre>';
        echo '</div>';
    }
}
add_action( 'wp_footer', 'dmrthema_debug_menu_html' );

// WordPress admin panelinde menu CSS siniflarini etkinlestir
add_filter('wp_nav_menu_args', function($args) {
    $args['walker'] = new Dmr_Walker_Nav_Menu();
    return $args;
});

// Menu CSS siniflarini goster
add_filter('wp_edit_nav_menu_walker', function() {
    return 'Walker_Nav_Menu_Edit';
});

// Menu CSS siniflarini etkinlestir
function dmrthema_enable_menu_css_classes() {
    add_filter('wp_nav_menu_args', function($args) {
        $args['walker'] = new Dmr_Walker_Nav_Menu();
        return $args;
    });
}
add_action('init', 'dmrthema_enable_menu_css_classes');



?>
